from odoo import models, fields, api
import logging

_logger = logging.getLogger(__name__)

class StockQuant(models.Model):
    _inherit = 'stock.quant'

    # 添加计算字段用于直接访问位置和批次名称
    location_name = fields.Char(
        string="Location Name",
        compute='_compute_display_names',
        store=False
    )

    lot_name = fields.Char(
        string="Lot Name",
        compute='_compute_display_names',
        store=False
    )

    @api.depends('location_id', 'lot_id')
    def _compute_display_names(self):
        """计算位置和批次名称"""
        for quant in self:
            quant.location_name = quant.location_id.name if quant.location_id else ''
            quant.lot_name = quant.lot_id.name if quant.lot_id else ''
    
    def name_get(self):
        """自定义 quant 的显示名称，格式：location_path - lot_name (如果有批次信息)"""
        result = []
        for quant in self:
            display_parts = []

            # 添加位置的完整路径信息
            if quant.location_id:
                # 获取位置的完整路径
                location_path = quant.location_id.complete_name or quant.location_id.name
                if location_path:
                    display_parts.append(location_path)

            # 如果有批次信息，直接使用批次名称（包含所有批次相关信息）
            if quant.lot_id and quant.lot_id.name:
                display_parts.append(quant.lot_id.name)
            else:
                # 如果没有批次信息，显示数量和产品代码
                if hasattr(quant, 'quantity') and quant.quantity:
                    display_parts.append(f"{quant.quantity:.2f}")

                if quant.product_id and hasattr(quant.product_id, 'default_code') and quant.product_id.default_code:
                    display_parts.append(quant.product_id.default_code)

            # 如果有任何信息，用 " - " 连接
            if display_parts:
                name = " - ".join(display_parts)
            else:
                # 备用显示
                name = f"Quant {quant.id}"
                if quant.product_id:
                    name = f"{quant.product_id.name} - {name}"

            result.append((quant.id, name))

        return result

    @api.model
    def _name_search(self, name, args=None, operator='ilike', limit=100, name_get_uid=None):
        """扩展搜索功能，支持按位置名称、批次名称等搜索"""
        print(f"[STOCK_QUANT] _name_search called with name: {name}")
        args = args or []
        
        if name:
            # 支持按位置名称搜索
            location_domain = [('location_id.name', operator, name)]
            # 支持按批次名称搜索
            lot_domain = [('lot_id.name', operator, name)]
            # 组合搜索条件
            search_domain = ['|'] + location_domain + ['|'] + lot_domain + [('id', operator, name)]
            args = search_domain + args
        
        result = super(StockQuant, self)._name_search(name, args, operator, limit, name_get_uid)
        print(f"[STOCK_QUANT] _name_search result: {result}")
        return result