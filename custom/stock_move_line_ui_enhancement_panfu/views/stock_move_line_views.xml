<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- ============ 继承标准视图以匹配客户要求的格式 ============ -->
    <record id="view_move_line_tree_receipt_inherited" model="ir.ui.view">
        <field name="name">stock.move.line.tree.receipt.inherited</field>
        <field name="model">stock.move.line</field>
        <field name="inherit_id" ref="stock.view_move_line_tree_detailed"/>
        <field name="arch" type="xml">
            <xpath expr="//tree" position="replace">
                <tree string="Move Lines" create="0" default_order="date">
                    <!-- 按照客户要求的字段显隐效果和排序 -->
                    <!-- 未勾选字段 - 默认隐藏 -->
                    <field name="date" string="Date" optional="hide"/>

                    <!-- 勾选字段 - 默认显示 -->
                    <field name="picking_type_id" string="Type of Operation" optional="show"/>
                    <field name="picking_partner_id" string="Partner" optional="show"/>
                    <field name="picking_id" string="Source Document" optional="show"/>
                    <field name="product_id" string="Product No." optional="show"/>

                    <!-- 未勾选字段 - 默认隐藏 -->
                    <field name="x_studio_vendor_item_no" string="Vendor Item No" optional="hide"/>
                    <field name="x_studio_version_no" string="Version Family" optional="hide"/>
                    <field name="reference" string="Reference" optional="hide"/>
                    <field name="lot_id" string="Lot/Serial Number" optional="hide" groups="stock.group_production_lot"/>

                    <!-- 勾选字段 - 默认显示 -->
                    <field name="x_studio_related_field_85n_1irfjfaer" string="Demand" optional="show"/>

                    <!-- 未勾选字段 - 默认隐藏 -->
                    <field name="quantity_product_uom" string="Quantity" optional="hide"/>

                    <!-- 勾选字段 - 默认显示 -->
                    <field name="x_studio_exmill_date_1" string="Exmill date" optional="show"/>
                    <field name="x_studio_exmill_date" string="Updated Exmill" optional="show"/>
                    <field name="x_studio_ready_date" string="Ready date" optional="show"/>
                    <field name="x_studio_ship_mode" string="Ship mode" optional="show"/>
                    <field name="x_studio_eta" string="ETA" optional="show"/>

                    <!-- 未勾选字段 - 默认隐藏 -->
                    <field name="location_id" string="From" optional="hide" groups="stock.group_stock_multi_locations"/>
                    <field name="location_dest_id" string="To" optional="hide" groups="stock.group_stock_multi_locations"/>

                    <!-- 勾选字段 - 默认显示 -->
                    <field name="state" string="Status" widget="badge" optional="show"
                           decoration-danger="state=='cancel'"
                           decoration-info="state== 'assigned'"
                           decoration-muted="state == 'draft'"
                           decoration-success="state == 'done'"
                           decoration-warning="state not in ('draft','cancel','done','assigned')"/>

                    <!-- 未勾选字段 - 默认隐藏 -->
                    <field name="company_id" string="Shipping Schedule" optional="hide" groups="base.group_multi_company"/>
                </tree>
            </xpath>
        </field>
    </record>

    <!-- ============ 内部调拨（转库）专用视图 ============ -->
    <record id="view_move_line_tree_internal" model="ir.ui.view">
        <field name="name">stock.move.line.tree.internal</field>
        <field name="model">stock.move.line</field>
        <field name="priority">1800</field>
        <field name="arch" type="xml">
            <tree string="Internal Transfer Details" create="0" edit="1" decoration-danger="display_warning == True">
                <!-- Hidden required fields -->
                <field name="company_id" column_invisible="True"/>
                <field name="tracking" column_invisible="True"/>
                <field name="move_id" column_invisible="True"/>
                <field name="picking_id" column_invisible="True"/>
                <field name="display_warning" column_invisible="True"/>
                <field name="picking_location_id" column_invisible="True"/>
                <field name="lot_id" column_invisible="False"/>
                <field name="is_locked" column_invisible="True"/>
                <field name="product_uom_category_id" column_invisible="True"/>
                <field name="state" column_invisible="True"/>
                <field name="location_id" column_invisible="True"/>
                
                <!-- Default visible fields (按图片顺序排列) -->
                <field name="product_id" string="Product" readonly="1" optional="show"/>
                <field name="x_studio_vendor_item_no" string="Vendor Item No" optional="show"/>

                <!-- Default hidden fields (黄色高亮字段 - 默认隐藏) -->
                <field name="x_studio_eta" string="ETA" optional="hide"/>
                <field name="x_studio_exmill_date_1" string="Exmill date" optional="hide"/>
                <field name="x_studio_exmill_date" string="Updated Exmill" optional="hide"/>

                <!-- Default visible location fields -->
                <field name="location_dest_id" string="To" required="1" optional="show"/>

                <field name="quant_id" string="Pick From" optional="show"
                       domain="[('product_id', '=', product_id), ('location_id', 'child_of', picking_location_id)]"
                       context="{'default_location_id': location_id, 'default_product_id': product_id, 'search_view_ref': 'stock.quant_search_view', 'tree_view_ref': 'stock.view_stock_quant_tree_simple', 'form_view_ref': 'stock.view_stock_quant_form', 'readonly_form': False}"
                       readonly="state in ('done', 'cancel') and is_locked"
                       widget="pick_from"
                       options="{'no_open': True, 'no_create': True}"
                       width="200px"/>

                <!-- Default visible quantity fields -->
                <field name="x_studio_related_field_85n_1irfjfaer" string="Demand" readonly="1" optional="show"/>
                <field name="quantity" string="Quantity" optional="show"/>
                <field name="product_uom_id" string="Unit of Measure" groups="uom.group_uom" optional="show"/>

                <!-- Default hidden fields (黄色高亮字段 - 默认隐藏) -->
                <field name="lot_qty_available" string="Available Quantity" optional="hide"/>
                <field name="display_warning" string="Display Warning" optional="hide" readonly="1"/>
                <field name="secondary_uom_qty" string="Secondary qty" optional="hide"/>
                <field name="secondary_uom_id" string="Secondary uom" optional="hide"/>
                <field name="x_studio_po_description" string="PO Description" optional="hide"/>
            </tree>
        </field>
    </record>
    
    <!-- ============ 收货专用视图 ============ -->
    <record id="view_move_line_tree_receipt" model="ir.ui.view">
        <field name="name">stock.move.line.tree.receipt</field>
        <field name="model">stock.move.line</field>
        <field name="priority">1</field>
        <field name="arch" type="xml">
            <tree string="Receipt Details" create="0" edit="1" default_order="product_id,lot_id" decoration-danger="display_warning == True">
                <!-- Hidden required fields -->
                <field name="company_id" column_invisible="True"/>
                <field name="tracking" column_invisible="True"/>
                <field name="move_id" column_invisible="True"/>
                <field name="picking_id" column_invisible="True"/>
                <field name="display_warning" column_invisible="True"/>
                <field name="product_uom_category_id" column_invisible="True"/>
                
                <!-- Main fields -->
                <field name="product_id" string="Product" readonly="1"/>
                <field name="x_studio_vendor_item_no" string="Vendor Item No" optional="show"/>
                <field name="shipping_sch_id" string="Shipping Schedule" optional="show"/>
                <field name="x_studio_eta" string="ETA" optional="show"/>
                <field name="x_studio_exmill_date" string="Updated Exmill date" optional="show"/>
                <field name="x_studio_exmill_date_1" string="Exmill" optional="show"/>
                
                <!-- Location field -->
                <field name="location_dest_id" string="To" required="1"/>

                
                <!-- Quantity fields -->
                <field name="x_studio_related_field_85n_1irfjfaer" string="Demand" readonly="1"/>
                <field name="gross_unit" string="Gross" optional="show"/>
                <field name="quantity" string="Quantity"/>
                <field name="selvedge_id" string="Selvedge" optional="show"/>
                
                <!-- Lot/Serial field -->
                <field name="lot_id" string="Lot/Serial Number" groups="stock.group_production_lot" context="{'default_product_id': product_id, 'default_company_id': company_id}"/>
                
                <!-- Unit of Measure -->
                <field name="product_uom_id" string="Unit of Measure" groups="uom.group_uom"/>
                
                <!-- Optional fields (yellow highlighted - default hidden) -->
                <field name="lot_qty_available" string="Available Quantity" optional="hide"/>
                
                <!-- Warning field - 显示为复选框 -->
                <field name="display_warning" string="Display Warning" optional="hide" readonly="1"/>
                
                <field name="secondary_uom_qty" string="Secondary qty" optional="hide"/>
                <field name="secondary_uom_id" string="Secondary uom" optional="hide"/>
                <field name="x_studio_po_description" string="PO Description" optional="hide"/>
            </tree>
        </field>
    </record>
    <record id="stock_move_line_action" model="ir.actions.act_window">
        <field name="name">Moves History</field>
        <field name="res_model">stock.move.line</field>
        <field name="context">{'create': 0, 'pivot_measures': ['quantity_product_uom', '__count__']}</field>
    </record>
</odoo>